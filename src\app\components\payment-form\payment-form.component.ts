import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { PaymentService } from '../../services/payment.service';

@Component({
  selector: 'app-payment-form',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './payment-form.component.html',
  styleUrl: './payment-form.component.scss'
})
export class PaymentFormComponent implements OnInit {
  @Input() amount!: number;
  @Input() reservationId!: string;
  @Output() success = new EventEmitter<string>();
  @Output() error = new EventEmitter<string>();

  paymentMethod: 'card' | 'paypal' = 'card';
  isProcessing = false;
  cardNumber = '';
  cardExpiry = '';
  cardCvc = '';
  cardName = '';

  constructor(private paymentService: PaymentService) {}

  ngOnInit(): void {}

  onPaymentMethodChange(method: 'card' | 'paypal'): void {
    this.paymentMethod = method;
  }

  onSubmitCardPayment(): void {
    if (!this.validateCardForm()) {
      return;
    }

    this.isProcessing = true;

    // Dans une application réelle, nous utiliserions Stripe.js pour collecter les détails de la carte
    // et créer un token de paiement avant de l'envoyer au serveur
    this.paymentService.createPaymentIntent(this.amount, this.reservationId).subscribe({
      next: ({ clientSecret, paymentIntentId }) => {
        // Simuler la confirmation du paiement
        this.paymentService.confirmCardPayment(paymentIntentId).subscribe({
          next: (result) => {
            if (result.success) {
              this.success.emit(paymentIntentId);
            } else {
              this.error.emit(result.error || 'Une erreur est survenue lors du paiement');
            }
            this.isProcessing = false;
          },
          error: (err) => {
            this.error.emit('Une erreur est survenue lors du traitement du paiement');
            this.isProcessing = false;
          }
        });
      },
      error: (err) => {
        this.error.emit('Une erreur est survenue lors de la création de l\'intention de paiement');
        this.isProcessing = false;
      }
    });
  }


  private validateCardForm(): boolean {
    // Validation simplifiée pour la démo
    if (!this.cardNumber || !this.cardExpiry || !this.cardCvc || !this.cardName) {
      this.error.emit('Veuillez remplir tous les champs de la carte');
      return false;
    }
    return true;
  }
}
