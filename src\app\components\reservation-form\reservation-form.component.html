<form [formGroup]="reservationForm" (ngSubmit)="onSubmit()" class="space-y-6">
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div>
      <label for="firstName" class="block text-sm font-medium text-gray-700 mb-1">
        Prénom
      </label>
      <input
        id="firstName"
        type="text"
        formControlName="firstName"
        class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
        [ngClass]="{'border-red-500': reservationForm.get('firstName')?.invalid && reservationForm.get('firstName')?.touched, 'border-gray-300': !(reservationForm.get('firstName')?.invalid && reservationForm.get('firstName')?.touched)}"
      />
      <div *ngIf="reservationForm.get('firstName')?.invalid && reservationForm.get('firstName')?.touched" class="mt-1 text-sm text-red-600">
        <div *ngIf="reservationForm.get('firstName')?.errors?.['required']">Le prénom est requis.</div>
        <div *ngIf="reservationForm.get('firstName')?.errors?.['minlength']">Le prénom doit contenir au moins 2 caractères.</div>
        <div *ngIf="reservationForm.get('firstName')?.errors?.['maxlength']">Le prénom ne doit pas dépasser 50 caractères.</div>
        <div *ngIf="reservationForm.get('firstName')?.errors?.['pattern']">Le prénom ne doit contenir que des lettres, espaces, apostrophes et tirets.</div>
      </div>
    </div>

    <div>
      <label for="lastName" class="block text-sm font-medium text-gray-700 mb-1">
        Nom
      </label>
      <input
        id="lastName"
        type="text"
        formControlName="lastName"
        class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
        [ngClass]="{'border-red-500': reservationForm.get('lastName')?.invalid && reservationForm.get('lastName')?.touched, 'border-gray-300': !(reservationForm.get('lastName')?.invalid && reservationForm.get('lastName')?.touched)}"
      />
      <div *ngIf="reservationForm.get('lastName')?.invalid && reservationForm.get('lastName')?.touched" class="mt-1 text-sm text-red-600">
        <div *ngIf="reservationForm.get('lastName')?.errors?.['required']">Le nom est requis.</div>
        <div *ngIf="reservationForm.get('lastName')?.errors?.['minlength']">Le nom doit contenir au moins 2 caractères.</div>
        <div *ngIf="reservationForm.get('lastName')?.errors?.['maxlength']">Le nom ne doit pas dépasser 50 caractères.</div>
        <div *ngIf="reservationForm.get('lastName')?.errors?.['pattern']">Le nom ne doit contenir que des lettres, espaces, apostrophes et tirets.</div>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div>
      <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
        Email
      </label>
      <input
        id="email"
        type="email"
        formControlName="email"
        class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
        [ngClass]="{'border-red-500': reservationForm.get('email')?.invalid && reservationForm.get('email')?.touched, 'border-gray-300': !(reservationForm.get('email')?.invalid && reservationForm.get('email')?.touched)}"
      />
      <div *ngIf="reservationForm.get('email')?.invalid && reservationForm.get('email')?.touched" class="mt-1 text-sm text-red-600">
        <div *ngIf="reservationForm.get('email')?.errors?.['required']">L'email est requis.</div>
        <div *ngIf="reservationForm.get('email')?.errors?.['email']">Adresse email invalide.</div>
        <div *ngIf="reservationForm.get('email')?.errors?.['maxlength']">L'email ne doit pas dépasser 100 caractères.</div>
      </div>
    </div>

    <div>
      <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">
        Téléphone
      </label>
      <input
        id="phone"
        type="tel"
        formControlName="phone"
        class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
        [ngClass]="{'border-red-500': reservationForm.get('phone')?.invalid && reservationForm.get('phone')?.touched, 'border-gray-300': !(reservationForm.get('phone')?.invalid && reservationForm.get('phone')?.touched)}"
      />
      <div *ngIf="reservationForm.get('phone')?.invalid && reservationForm.get('phone')?.touched" class="mt-1 text-sm text-red-600">
        <div *ngIf="reservationForm.get('phone')?.errors?.['required']">Le téléphone est requis.</div>
        <div *ngIf="reservationForm.get('phone')?.errors?.['minlength']">Le numéro de téléphone doit contenir au moins 8 caractères.</div>
        <div *ngIf="reservationForm.get('phone')?.errors?.['maxlength']">Le numéro de téléphone ne doit pas dépasser 20 caractères.</div>
        <div *ngIf="reservationForm.get('phone')?.errors?.['pattern']">Veuillez entrer un numéro de téléphone valide.</div>
      </div>
    </div>
  </div>

  <div>
    <label for="numberOfGuests" class="block text-sm font-medium text-gray-700 mb-1">
      Nombre de personnes
    </label>
    <select
      id="numberOfGuests"
      formControlName="numberOfGuests"
      class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
      [ngClass]="{'border-red-500': reservationForm.get('numberOfGuests')?.invalid, 'border-gray-300': !reservationForm.get('numberOfGuests')?.invalid}"
    >
      <option *ngFor="let num of guestOptions" [value]="num">
        {{ num }} {{ num === 1 ? 'personne' : 'personnes' }}
      </option>
    </select>
    <div *ngIf="reservationForm.get('numberOfGuests')?.invalid" class="mt-1 text-sm text-red-600">
      <div *ngIf="reservationForm.get('numberOfGuests')?.errors?.['required']">Le nombre de personnes est requis.</div>
      <div *ngIf="reservationForm.get('numberOfGuests')?.errors?.['min']">Minimum 1 personne.</div>
      <div *ngIf="reservationForm.get('numberOfGuests')?.errors?.['max']">Maximum {{ maxGuests }} personnes.</div>
    </div>
  </div>

  <div>
    <label for="specialRequests" class="block text-sm font-medium text-gray-700 mb-1">
      Demandes spéciales (optionnel)
    </label>
    <textarea
      id="specialRequests"
      rows="3"
      formControlName="specialRequests"
      class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
      [ngClass]="{'border-red-500': reservationForm.get('specialRequests')?.invalid && reservationForm.get('specialRequests')?.touched, 'border-gray-300': !(reservationForm.get('specialRequests')?.invalid && reservationForm.get('specialRequests')?.touched)}"
      maxlength="500"
    ></textarea>
    <div class="flex justify-between mt-1">
      <div *ngIf="reservationForm.get('specialRequests')?.invalid && reservationForm.get('specialRequests')?.touched" class="text-sm text-red-600">
        <div *ngIf="reservationForm.get('specialRequests')?.errors?.['maxlength']">Le texte ne doit pas dépasser 500 caractères.</div>
      </div>
      <div class="text-xs text-gray-500">
        {{ (reservationForm.get('specialRequests')?.value?.length || 0) }} / 500
      </div>
    </div>
  </div>

  <div class="bg-gray-50 p-4 rounded-md">
    <h4 class="text-lg font-medium text-gray-900 mb-2">Récapitulatif</h4>
    <div class="space-y-2">
      <div class="flex justify-between">
        <span class="text-gray-600">Événement:</span>
        <span class="font-medium">{{ event.title }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-600">Table:</span>
        <span class="font-medium">Table {{ selectedTable?.tableNumber }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-600">Nombre de personnes:</span>
        <span class="font-medium">{{ reservationForm.get('numberOfGuests')?.value }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-600">Prix par personne:</span>
        <span class="font-medium">{{ event.pricePerPerson }} TND</span>
      </div>
      <div class="border-t border-gray-200 pt-2 mt-2">
        <div class="flex justify-between font-semibold">
          <span>Total:</span>
          <span>{{ totalPrice }} TND</span>
        </div>
      </div>
    </div>
  </div>

  <div>
    <button
      type="submit"
      [disabled]="reservationForm.invalid || isSubmitting || !selectedTableId"
      class="w-full bg-gold-600 hover:bg-gold-700 text-white py-3 px-4 rounded-md font-medium transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
    >
      <span *ngIf="!isSubmitting">Continuer vers le paiement</span>
      <span *ngIf="isSubmitting" class="flex justify-center items-center">
        <span class="animate-spin h-5 w-5 mr-3 border-t-2 border-b-2 border-white rounded-full"></span>
        Traitement en cours...
      </span>
    </button>
  </div>
</form>
