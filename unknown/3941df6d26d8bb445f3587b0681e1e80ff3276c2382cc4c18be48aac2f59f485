import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EventService } from '../../services/event.service';
import { Event } from '../../models/event.model';
import { EventCardComponent } from '../event-card/event-card.component';

@Component({
  selector: 'app-event-test',
  standalone: true,
  imports: [CommonModule, EventCardComponent],
  templateUrl: './event-test.component.html',
  styleUrl: './event-test.component.scss'
})
export class EventTestComponent implements OnInit {
  events: Event[] = [];
  isLoading = true;
  errorMessage = '';

  // Sample JSON data from Strapi
  private sampleData = {
    "data": [
      {
        "id": 2,
        "documentId": "dueu0q5qmbtgdx62o7f7jmui",
        "name": "Soirée Gastronomique Tunisienne",
        "price": "120",
        "description": "Découvrez les saveurs authentiques de la cuisine tunisienne lors de cette soirée exceptionnelle.",
        "date": "2025-05-07T14:30:00.000Z",
        "createdAt": "2025-05-05T19:54:12.619Z",
        "updatedAt": "2025-05-05T19:54:12.619Z",
        "publishedAt": "2025-05-05T19:54:12.662Z",
        "max_attendees": 45,
        "duration": 2,
        "is_active": true,
        "icon": null,
        "images": [
          {
            "id": 1,
            "documentId": "cjqcc3nlloq6jfqnang5kjov",
            "name": "drakeIcon.png",
            "alternativeText": null,
            "caption": null,
            "width": 467,
            "height": 418,
            "formats": {
              "thumbnail": {
                "name": "thumbnail_drakeIcon.png",
                "hash": "thumbnail_drake_Icon_e65a142acc",
                "ext": ".png",
                "mime": "image/png",
                "path": null,
                "width": 174,
                "height": 156,
                "size": 52.87,
                "sizeInBytes": 52867,
                "url": "/uploads/thumbnail_drake_Icon_e65a142acc.png"
              }
            },
            "hash": "drake_Icon_e65a142acc",
            "ext": ".png",
            "mime": "image/png",
            "size": 69.82,
            "url": "/uploads/drake_Icon_e65a142acc.png",
            "previewUrl": null,
            "provider": "local",
            "provider_metadata": null,
            "createdAt": "2025-05-05T19:53:44.032Z",
            "updatedAt": "2025-05-05T19:53:44.032Z",
            "publishedAt": "2025-05-05T19:53:44.034Z"
          },
          {
            "id": 2,
            "documentId": "quhmzw9r6e6yqg1gjdfil105",
            "name": "drakeGenerated.png",
            "alternativeText": null,
            "caption": null,
            "width": 720,
            "height": 1280,
            "formats": {
              "thumbnail": {
                "name": "thumbnail_drakeGenerated.png",
                "hash": "thumbnail_drake_Generated_21a1cdb91e",
                "ext": ".png",
                "mime": "image/png",
                "path": null,
                "width": 88,
                "height": 156,
                "size": 27.78,
                "sizeInBytes": 27779,
                "url": "/uploads/thumbnail_drake_Generated_21a1cdb91e.png"
              },
              "small": {
                "name": "small_drakeGenerated.png",
                "hash": "small_drake_Generated_21a1cdb91e",
                "ext": ".png",
                "mime": "image/png",
                "path": null,
                "width": 281,
                "height": 500,
                "size": 198.1,
                "sizeInBytes": 198097,
                "url": "/uploads/small_drake_Generated_21a1cdb91e.png"
              },
              "medium": {
                "name": "medium_drakeGenerated.png",
                "hash": "medium_drake_Generated_21a1cdb91e",
                "ext": ".png",
                "mime": "image/png",
                "path": null,
                "width": 422,
                "height": 750,
                "size": 418.67,
                "sizeInBytes": 418673,
                "url": "/uploads/medium_drake_Generated_21a1cdb91e.png"
              },
              "large": {
                "name": "large_drakeGenerated.png",
                "hash": "large_drake_Generated_21a1cdb91e",
                "ext": ".png",
                "mime": "image/png",
                "path": null,
                "width": 563,
                "height": 1000,
                "size": 724.81,
                "sizeInBytes": 724806,
                "url": "/uploads/large_drake_Generated_21a1cdb91e.png"
              }
            },
            "hash": "drake_Generated_21a1cdb91e",
            "ext": ".png",
            "mime": "image/png",
            "size": 237.83,
            "url": "/uploads/drake_Generated_21a1cdb91e.png",
            "previewUrl": null,
            "provider": "local",
            "provider_metadata": null,
            "createdAt": "2025-05-05T19:53:53.621Z",
            "updatedAt": "2025-05-05T19:53:53.621Z",
            "publishedAt": "2025-05-05T19:53:53.622Z"
          }
        ],
        "reservations": []
      }
    ],
    "meta": {
      "pagination": {
        "page": 1,
        "pageSize": 25,
        "pageCount": 1,
        "total": 1
      }
    }
  };

  constructor(private eventService: EventService) {}

  ngOnInit(): void {
    this.parseJsonData();
  }

  parseJsonData(): void {
    try {
      this.isLoading = true;
      this.events = this.eventService.parseEventsFromJson(this.sampleData);
      this.isLoading = false;
    } catch (error) {
      console.error('Error parsing JSON data:', error);
      this.errorMessage = 'Une erreur est survenue lors du traitement des données.';
      this.isLoading = false;
    }
  }
}
