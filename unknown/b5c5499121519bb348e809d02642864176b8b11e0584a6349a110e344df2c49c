import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-image-carousel',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './image-carousel.component.html',
  styleUrl: './image-carousel.component.scss',
  animations: [
    trigger('carouselAnimation', [
      transition('void => *', [
        style({ opacity: 0 }),
        animate('300ms', style({ opacity: 1 }))
      ]),
      transition('* => void', [
        animate('300ms', style({ opacity: 0 }))
      ])
    ])
  ]
})
export class ImageCarouselComponent implements OnInit, OnChanges {
  @Input() images: string[] = [];
  @Input() defaultImage: string = 'assets/images/default-event.jpg';
  @Input() iconUrl: string | null = null;
  @Input() autoPlay: boolean = true;
  @Input() interval: number = 5000; // 5 seconds
  @Input() height: string = '400px';

  currentIndex = 0;
  displayImages: string[] = [];
  private autoPlayInterval: any;

  // Computed property for current image URL
  get currentImageUrl(): string {
    if (this.displayImages.length > 0 && this.currentIndex < this.displayImages.length) {
      return this.displayImages[this.currentIndex];
    }
    return this.defaultImage;
  }

  ngOnInit(): void {
    this.setupImages();
    this.startAutoPlay();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['images'] || changes['iconUrl'] || changes['defaultImage']) {
      this.setupImages();
    }

    if (changes['autoPlay'] || changes['interval']) {
      this.resetAutoPlay();
    }
  }

  private setupImages(): void {
    this.displayImages = [];

    // Add images if available
    if (this.images && this.images.length > 0) {
      this.displayImages = [...this.images].filter((url: string) => !!url);
    }

    // Add icon if available and not already in images
    if (this.iconUrl && this.iconUrl.trim() !== '') {
      if (!this.displayImages.includes(this.iconUrl)) {
        this.displayImages.unshift(this.iconUrl);
      }
    }

    // If no images, use default
    if (this.displayImages.length === 0) {
      this.displayImages = [this.defaultImage];
    }

    this.currentIndex = 0;
  }

  private startAutoPlay(): void {
    if (this.autoPlay && this.displayImages.length > 1) {
      this.autoPlayInterval = setInterval(() => {
        this.next();
      }, this.interval);
    }
  }

  private resetAutoPlay(): void {
    if (this.autoPlayInterval) {
      clearInterval(this.autoPlayInterval);
      this.autoPlayInterval = null;
    }

    this.startAutoPlay();
  }

  next(): void {
    this.currentIndex = (this.currentIndex + 1) % this.displayImages.length;
  }

  prev(): void {
    this.currentIndex = (this.currentIndex - 1 + this.displayImages.length) % this.displayImages.length;
  }

  goToImage(index: number): void {
    this.currentIndex = index;
  }

  pauseAutoPlay(): void {
    if (this.autoPlayInterval) {
      clearInterval(this.autoPlayInterval);
      this.autoPlayInterval = null;
    }
  }

  resumeAutoPlay(): void {
    if (this.autoPlay && !this.autoPlayInterval && this.displayImages.length > 1) {
      this.startAutoPlay();
    }
  }

  /**
   * Handle image loading errors
   */
  handleImageError(event: Event): void {
    const imgElement = event.target as HTMLImageElement;

    // Simply replace with default image
    imgElement.src = this.defaultImage;

    // If we're showing the current image and it failed, move to the next one if available
    if (this.displayImages.length > 1) {
      this.next();
    }
  }

  ngOnDestroy(): void {
    if (this.autoPlayInterval) {
      clearInterval(this.autoPlayInterval);
    }
  }
}
