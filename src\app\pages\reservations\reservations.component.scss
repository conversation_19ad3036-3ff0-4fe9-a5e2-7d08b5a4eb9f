// Line clamp utility for text truncation
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Ensure images are responsive and maintain aspect ratio
.reservation-image {
  transition: transform 0.3s ease;
}

.reservation-image:hover {
  transform: scale(1.02);
}

// Status badge styling improvements
.status-badge {
  backdrop-filter: blur(4px);
  background-color: rgba(255, 255, 255, 0.9);
}