// src/app/payment/payment.component.spec.ts
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { PaymentComponent } from './payment.component';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

describe('PaymentComponent', () => {
  let component: PaymentComponent;
  let fixture: ComponentFixture<PaymentComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CommonModule, FormsModule, HttpClientTestingModule, PaymentComponent],
      providers: [
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { paramMap: { get: () => '1' } }
          }
        }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(PaymentComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize reservationDetails and orderSummary on ngOnInit', async () => {
    spyOn(component['http'], 'get').and.returnValue(of({
      data: {
        id: '1',
        attributes: {
          event: { data: { attributes: { title: 'Soirée Gastronomique' } } },
          booking_date: '2024-12-15',
          event_time: '19:30',
          table_number: '12',
          ticket_count: 4,
          price_per_person: 85,
          total_price: 380.50,
          service_fee: 15,
          taxes: 25.50,
          reservation_id: 'RES-2024-001234',
          status: 'pending'
        }
      }
    }));
    await component.ngOnInit();
    expect(component.reservationDetails.eventTitle).toBe('Soirée Gastronomique');
    expect(component.orderSummary.total).toBe(380.50);
  });
});