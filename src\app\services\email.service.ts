import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class EmailService {
  private apiUrl = environment.apiUrl || 'http://localhost:1337/api';

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  /**
   * Create headers for API requests
   */
  private createHeaders(): HttpHeaders {
    const token = this.authService.getToken();
    let headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    }

    return headers;
  }

  /**
   * Send reservation confirmation email manually
   */
  sendReservationConfirmation(reservationId: string): Observable<any> {
    const headers = this.createHeaders();
    return this.http.post(
      `${this.apiUrl}/email/reservation-confirmation/${reservationId}`,
      {},
      { headers }
    );
  }

  /**
   * Send payment confirmation email manually
   */
  sendPaymentConfirmation(reservationId: string): Observable<any> {
    const headers = this.createHeaders();
    return this.http.post(
      `${this.apiUrl}/email/payment-confirmation/${reservationId}`,
      {},
      { headers }
    );
  }

  /**
   * Send test email
   */
  sendTestEmail(email: string): Observable<any> {
    const headers = this.createHeaders();
    return this.http.post(
      `${this.apiUrl}/email/test`,
      { email },
      { headers }
    );
  }

  /**
   * Test email configuration
   */
  testEmailConfig(): Observable<any> {
    const headers = this.createHeaders();
    return this.http.get(
      `${this.apiUrl}/email/test-config`,
      { headers }
    );
  }
}
