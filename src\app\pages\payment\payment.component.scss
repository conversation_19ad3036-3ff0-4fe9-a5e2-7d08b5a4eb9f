// Payment component specific styles
.payment-method-card {
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &.selected {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.2);
  }
}

.card-input {
  transition: all 0.2s ease-in-out;

  &:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.1);
  }
}

.payment-button {
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  &:active {
    transform: translateY(0);
  }
}

.order-summary {
  background: linear-gradient(135deg, #ffffff 0%, #f9f9f9 100%);
}

.security-badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .payment-grid {
    grid-template-columns: 1fr;
  }

  .payment-method-grid {
    grid-template-columns: 1fr;
  }
}

// Custom gold theme enhancements
.gold-gradient {
  background: linear-gradient(135deg, #d4af37 0%, #c4a028 100%);
}

.gold-border {
  border-color: #d4af37;
}

.gold-shadow {
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}