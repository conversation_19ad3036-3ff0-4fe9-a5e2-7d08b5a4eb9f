// src/app/models/reservation.model.ts
export interface Reservation {
  id: string;
  userId: string;
  eventId: string;
  eventTitle?: string; // Fetched separately or populated
  tableNumber?: string; // Fetched separately or computed
  ticket_count: number;
  statutR: 'pending' | 'confirmed' | 'cancelled';
  total_price: number;
  numberOfGuests?: number; // Optional, may be computed from ticket_count
  booking_date: string;
  event_time?: string; // Optional, may be in event
  price_per_person?: number; // Computed or from event
  service_fee?: number; // Computed
  taxes?: number; // Computed
  payment_reference?: string;
  specialRequests?: string;
  createdAt: string;
  updatedAt: string;
}

export interface StrapiReservationAttributes {
  ticket_count: number;
  statutR: 'pending' | 'confirmed' | 'cancelled' | null;
  total_price: number;
  booking_date: string | null;
  payment_reference: string | null;
  user: number;
  event: number;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
}

export interface StrapiSingleResponse {
  data: {
    id: number;
    attributes: Reservation;
  };
  meta: {};
}

export interface ReservationFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  numberOfGuests: number;
  specialRequests?: string;
}