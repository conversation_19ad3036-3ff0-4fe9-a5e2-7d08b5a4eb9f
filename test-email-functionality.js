/**
 * Email Functionality Test Script
 * 
 * This script tests the email functionality of your hotel booking application.
 * Run this script after setting up your Strapi backend with email configuration.
 * 
 * Usage:
 * node test-email-functionality.js
 */

const axios = require('axios');

// Configuration
const STRAPI_URL = 'http://localhost:1337';
const API_URL = `${STRAPI_URL}/api`;
const TEST_EMAIL = '<EMAIL>'; // Change this to your test email

// Test credentials - you'll need to create a test user or use existing credentials
const TEST_USER = {
  identifier: '<EMAIL>', // Your test user email
  password: 'testpassword123'     // Your test user password
};

let authToken = '';

/**
 * Authenticate and get JWT token
 */
async function authenticate() {
  try {
    console.log('🔐 Authenticating...');
    const response = await axios.post(`${API_URL}/auth/local`, TEST_USER);
    authToken = response.data.jwt;
    console.log('✅ Authentication successful');
    return true;
  } catch (error) {
    console.error('❌ Authentication failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Create headers with authentication
 */
function createHeaders() {
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`
  };
}

/**
 * Test 1: Email Configuration Test
 */
async function testEmailConfiguration() {
  try {
    console.log('\n📧 Testing email configuration...');
    const response = await axios.get(`${API_URL}/email/test-config`, {
      headers: createHeaders()
    });
    console.log('✅ Email configuration test passed');
    console.log('📋 Config:', response.data.config);
    return true;
  } catch (error) {
    console.error('❌ Email configuration test failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Test 2: Send Test Email
 */
async function sendTestEmail() {
  try {
    console.log('\n📨 Sending test email...');
    const response = await axios.post(`${API_URL}/email/test`, 
      { email: TEST_EMAIL },
      { headers: createHeaders() }
    );
    console.log('✅ Test email sent successfully');
    console.log('📧 Sent to:', response.data.data.email);
    return true;
  } catch (error) {
    console.error('❌ Test email failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Test 3: Get Sample Reservation for Testing
 */
async function getSampleReservation() {
  try {
    console.log('\n🔍 Looking for sample reservation...');
    const response = await axios.get(`${API_URL}/reservations?populate=*`, {
      headers: createHeaders()
    });
    
    if (response.data.data && response.data.data.length > 0) {
      const reservation = response.data.data[0];
      console.log('✅ Found sample reservation:', reservation.documentId);
      return reservation.documentId;
    } else {
      console.log('⚠️  No reservations found. Create a reservation first to test email functionality.');
      return null;
    }
  } catch (error) {
    console.error('❌ Failed to get reservations:', error.response?.data || error.message);
    return null;
  }
}

/**
 * Test 4: Send Reservation Confirmation Email
 */
async function testReservationConfirmationEmail(reservationId) {
  try {
    console.log('\n📧 Testing reservation confirmation email...');
    const response = await axios.post(`${API_URL}/email/reservation-confirmation/${reservationId}`, 
      {},
      { headers: createHeaders() }
    );
    console.log('✅ Reservation confirmation email sent successfully');
    console.log('📧 Sent to:', response.data.data.email);
    return true;
  } catch (error) {
    console.error('❌ Reservation confirmation email failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Test 5: Send Payment Confirmation Email
 */
async function testPaymentConfirmationEmail(reservationId) {
  try {
    console.log('\n💳 Testing payment confirmation email...');
    const response = await axios.post(`${API_URL}/email/payment-confirmation/${reservationId}`, 
      {},
      { headers: createHeaders() }
    );
    console.log('✅ Payment confirmation email sent successfully');
    console.log('📧 Sent to:', response.data.data.email);
    return true;
  } catch (error) {
    console.error('❌ Payment confirmation email failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🚀 Starting Email Functionality Tests');
  console.log('=====================================');
  
  // Check if axios is available
  if (!axios) {
    console.error('❌ axios is required. Install it with: npm install axios');
    return;
  }

  let passedTests = 0;
  let totalTests = 0;

  // Test 1: Authentication
  totalTests++;
  if (await authenticate()) {
    passedTests++;
  } else {
    console.log('❌ Cannot proceed without authentication');
    return;
  }

  // Test 2: Email Configuration
  totalTests++;
  if (await testEmailConfiguration()) {
    passedTests++;
  }

  // Test 3: Send Test Email
  totalTests++;
  if (await sendTestEmail()) {
    passedTests++;
  }

  // Test 4: Get Sample Reservation
  const reservationId = await getSampleReservation();
  
  if (reservationId) {
    // Test 5: Reservation Confirmation Email
    totalTests++;
    if (await testReservationConfirmationEmail(reservationId)) {
      passedTests++;
    }

    // Test 6: Payment Confirmation Email
    totalTests++;
    if (await testPaymentConfirmationEmail(reservationId)) {
      passedTests++;
    }
  } else {
    console.log('⚠️  Skipping reservation email tests - no reservations found');
  }

  // Summary
  console.log('\n📊 Test Results');
  console.log('================');
  console.log(`✅ Passed: ${passedTests}/${totalTests} tests`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Email functionality is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Check the error messages above and your configuration.');
  }

  console.log('\n📝 Next Steps:');
  console.log('1. Check your email inbox for test emails');
  console.log('2. Create a new reservation through the frontend to test automatic emails');
  console.log('3. Complete a payment to test payment confirmation emails');
  console.log('4. Check Strapi console logs for any error messages');
}

// Run the tests
runTests().catch(error => {
  console.error('💥 Test script failed:', error.message);
});

// Export for use as module
module.exports = {
  runTests,
  authenticate,
  testEmailConfiguration,
  sendTestEmail,
  testReservationConfirmationEmail,
  testPaymentConfirmationEmail
};
