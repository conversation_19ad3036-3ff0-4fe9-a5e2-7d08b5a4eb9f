<div class="max-w-md mx-auto">
  <div class="mb-6">
    <div class="flex justify-center space-x-4 mb-6">
      <button
        type="button"
        class="flex items-center px-4 py-2 rounded-md"
        [ngClass]="paymentMethod === 'card' ? 'bg-gold-600 text-white' : 'bg-gray-100 text-gray-700'"
        (click)="onPaymentMethodChange('card')"
      >
        <span class="material-icons mr-2">credit_card</span>
        Carte bancaire
      </button>
    </div>

    <div *ngIf="paymentMethod === 'card'" class="space-y-6">
      <div class="border border-gray-300 rounded-md p-4 space-y-4">
        <div>
          <label for="cardName" class="block text-sm font-medium text-gray-700 mb-1">Nom sur la carte</label>
          <input
            id="cardName"
            type="text"
            [(ngModel)]="cardName"
            class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
            placeholder="<PERSON>"
          />
        </div>
        <div>
          <label for="cardNumber" class="block text-sm font-medium text-gray-700 mb-1">Numéro de carte</label>
          <input
            id="cardNumber"
            type="text"
            [(ngModel)]="cardNumber"
            class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
            placeholder="4242 4242 4242 4242"
          />
        </div>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label for="cardExpiry" class="block text-sm font-medium text-gray-700 mb-1">Date d'expiration</label>
            <input
              id="cardExpiry"
              type="text"
              [(ngModel)]="cardExpiry"
              class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
              placeholder="MM/AA"
            />
          </div>
          <div>
            <label for="cardCvc" class="block text-sm font-medium text-gray-700 mb-1">CVC</label>
            <input
              id="cardCvc"
              type="text"
              [(ngModel)]="cardCvc"
              class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
              placeholder="123"
            />
          </div>
        </div>
      </div>
      
      <div class="flex items-center text-sm text-gray-600 mb-4">
        <span class="material-icons mr-2">lock</span>
        <span>Paiement  Stripe</span>
      </div>
      
      <button
        type="button"
        (click)="onSubmitCardPayment()"
        [disabled]="isProcessing"
        class="w-full bg-gold-600 hover:bg-gold-700 text-white py-3 px-4 rounded-md font-medium transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
      >
        {{ isProcessing ? 'Traitement en cours...' : 'Payer ' + amount + ' TND' }}
      </button>
    </div>

  </div>
  
  <div class="border-t border-gray-200 pt-4">
    <h4 class="text-lg font-medium text-gray-900 mb-2">Montant à payer</h4>
    <div class="text-2xl font-bold text-gold-600">{{ amount }} TND</div>
  </div>
</div>
