<div
  class="carousel-container relative overflow-hidden rounded-lg"
  [style.height]="height"
  (mouseenter)="pauseAutoPlay()"
  (mouseleave)="resumeAutoPlay()"
>
  <!-- Simple Image Display (like event-card) -->
  <div class="w-full h-full">
    <img
      [src]="currentImageUrl"
      [alt]="'Event image'"
      class="w-full h-full object-cover"
      (error)="handleImageError($event)"
    />
  </div>

  <!-- Navigation Arrows -->
  <div class="carousel-controls absolute inset-0 flex items-center justify-between px-4">
    <button
      *ngIf="displayImages.length > 1"
      (click)="prev()"
      class="carousel-arrow bg-black bg-opacity-30 hover:bg-opacity-50 text-white rounded-full p-2 transition-all duration-300"
      aria-label="Previous image"
    >
      <span class="material-icons">chevron_left</span>
    </button>
    <button
      *ngIf="displayImages.length > 1"
      (click)="next()"
      class="carousel-arrow bg-black bg-opacity-30 hover:bg-opacity-50 text-white rounded-full p-2 transition-all duration-300"
      aria-label="Next image"
    >
      <span class="material-icons">chevron_right</span>
    </button>
  </div>

  <!-- Indicators -->
  <div
    *ngIf="displayImages.length > 1"
    class="carousel-indicators absolute bottom-4 left-0 right-0 flex justify-center space-x-2"
  >
    <button
      *ngFor="let image of displayImages; let i = index"
      (click)="goToImage(i)"
      class="w-3 h-3 rounded-full transition-all duration-300"
      [ngClass]="i === currentIndex ? 'bg-white' : 'bg-white bg-opacity-50 hover:bg-opacity-75'"
      [attr.aria-label]="'Go to image ' + (i + 1)"
    ></button>
  </div>
</div>
