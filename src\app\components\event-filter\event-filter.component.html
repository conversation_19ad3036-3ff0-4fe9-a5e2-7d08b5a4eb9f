<div class="bg-white rounded-lg shadow-md p-6 mb-8">
  <h2 class="text-xl font-semibold text-gray-900 mb-4">Filtrer les événements</h2>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div class="relative">
      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <span class="material-icons text-gray-400">search</span>
      </div>
      <input
        type="text"
        placeholder="Rechercher un événement"
        class="pl-10 w-full border border-gray-300 rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent"
        [(ngModel)]="search"
        (ngModelChange)="onSearchChange()"
      />
    </div>
    <div class="relative">
      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <span class="material-icons text-gray-400">event</span>
      </div>
      <input
        type="date"
        class="pl-10 w-full border border-gray-300 rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent"
        [(ngModel)]="date"
        (ngModelChange)="onDateChange()"
      />
    </div>
  </div>
</div>
