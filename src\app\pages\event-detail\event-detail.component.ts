import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { EventService } from '../../services/event.service';
import { ReservationService } from '../../services/reservation.service';
import { AuthService } from '../../services/auth.service';
import { Event } from '../../models/event.model';
import { Table } from '../../models/table.model';
import { ReservationFormComponent } from '../../components/reservation-form/reservation-form.component';
import { ReservationFormData } from '../../models/reservation.model';
import { ImageCarouselComponent } from '../../components/image-carousel/image-carousel.component';

@Component({
  selector: 'app-event-detail',
  standalone: true,
  imports: [CommonModule, RouterModule, ReservationFormComponent, ImageCarouselComponent],
  templateUrl: './event-detail.component.html',
  styleUrl: './event-detail.component.scss'
})
export class EventDetailComponent implements OnInit {
  event: Event | undefined;
  tables: Table[] = [];
  selectedTableId: string | null = null;
  selectedTable: Table | undefined;
  isLoading = true;
  errorMessage = '';
  showReservationForm = false;
  isAuthenticated = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private eventService: EventService,
    private reservationService: ReservationService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.isAuthenticated = this.authService.isAuthenticated();
    this.loadEventDetails();
  }

  private loadEventDetails(): void {
    const eventId = this.route.snapshot.paramMap.get('id');
    if (!eventId) {
      this.errorMessage = 'Événement non trouvé.';
      this.isLoading = false;
      return;
    }

    this.eventService.getEventById(eventId).subscribe({
      next: (event) => {
        if (!event) {
          this.errorMessage = 'Événement non trouvé.';
          this.isLoading = false;
          return;
        }

        this.event = event;

        // Ensure images array exists
        if (!this.event.images) {
          this.event.images = [];
        }

        // If we have a main image URL but no images, add the main image to the images array
        if (this.event.imageUrl && (!this.event.images || this.event.images.length === 0)) {
          this.event.images = [this.event.imageUrl];
        }

        this.loadTables(eventId);
      },
      error: (error) => {
        console.error('Error loading event:', error);
        this.errorMessage = 'Une erreur est survenue lors du chargement de l\'événement.';
        this.isLoading = false;
      }
    });
  }

  private loadTables(eventId: string): void {
    this.isLoading = true;
    this.reservationService.getTablesByEventId(eventId).subscribe({
      next: (tables) => {
        this.tables = tables;
        this.isLoading = false;

        console.log(`Loaded ${tables.length} tables for event ${eventId}`);
        console.log('Total capacity:', tables.reduce((sum, table) => sum + table.capacity, 0));

        if (this.tables.length === 0) {
          this.errorMessage = 'Aucune table disponible pour cet événement.';
        }
      },
      error: (error) => {
        console.error('Error loading tables:', error);
        this.errorMessage = 'Une erreur est survenue lors du chargement des tables.';
        this.isLoading = false;
      }
    });
  }

  selectTable(tableId: string): void {
    if (this.selectedTableId === tableId) {
      this.selectedTableId = null;
      this.selectedTable = undefined;
      this.showReservationForm = false;
      return;
    }

    this.selectedTableId = tableId;
    this.selectedTable = this.tables.find(t => t.id === tableId);
    this.showReservationForm = true;
  }

  isTableAvailable(table: Table): boolean {
    return table.isAvailable;
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', { day: 'numeric', month: 'long', year: 'numeric' });
  }



  onReservationSubmit(formData: ReservationFormData): void {
    if (!this.event || !this.selectedTableId) {
      return;
    }

    const totalPrice = this.event.pricePerPerson * formData.numberOfGuests;

    this.reservationService.createReservation(
      this.event.id,
      this.selectedTableId,
      formData,
      totalPrice
    ).subscribe({
      next: (reservation) => {
        // Navigate with documentId (reservation.id now contains documentId)
        this.router.navigate(['/payment', reservation.id]);
      },
      error: (error) => {
        console.error('Error creating reservation:', error);
        this.errorMessage = 'Une erreur est survenue lors de la création de la réservation.';
      }
    });
  }

  onLogin(): void {
    this.router.navigate(['/account'], { queryParams: { returnUrl: this.router.url } });
  }
}
