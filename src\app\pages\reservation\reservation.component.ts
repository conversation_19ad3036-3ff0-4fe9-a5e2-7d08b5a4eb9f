import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { EventService } from '../../services/event.service';
import { ReservationService } from '../../services/reservation.service';
import { AuthService } from '../../services/auth.service';
import { Event } from '../../models/event.model';
import { Table } from '../../models/table.model';
import { ReservationFormComponent } from '../../components/reservation-form/reservation-form.component';
import { ReservationFormData } from '../../models/reservation.model';

@Component({
  selector: 'app-reservation',
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule, ReservationFormComponent],
  templateUrl: './reservation.component.html',
  styleUrl: './reservation.component.scss'
})
export class ReservationComponent implements OnInit {
  event: Event | undefined;
  tables: Table[] = [];
  selectedTableId: string | null = null;
  selectedTable: Table | undefined;
  isLoading = true;
  errorMessage = '';
  isAuthenticated = false;
  step: 'table-selection' | 'form' = 'table-selection';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private eventService: EventService,
    private reservationService: ReservationService,
    private authService: AuthService,
    private fb: FormBuilder
  ) {}

  ngOnInit(): void {
    this.isAuthenticated = this.authService.isAuthenticated();

    if (!this.isAuthenticated) {
      this.router.navigate(['/account'], { queryParams: { returnUrl: this.router.url } });
      return;
    }

    this.loadEventDetails();
  }

  private loadEventDetails(): void {
    const eventId = this.route.snapshot.paramMap.get('eventId');
    if (!eventId) {
      this.errorMessage = 'Événement non trouvé.';
      this.isLoading = false;
      return;
    }

    this.eventService.getEventById(eventId).subscribe({
      next: (event) => {
        if (!event) {
          this.errorMessage = 'Événement non trouvé.';
          this.isLoading = false;
          return;
        }

        this.event = event;
        this.loadTables(eventId);
      },
      error: (error) => {
        console.error('Error loading event:', error);
        this.errorMessage = 'Une erreur est survenue lors du chargement de l\'événement.';
        this.isLoading = false;
      }
    });
  }

  private loadTables(eventId: string): void {
    this.reservationService.getTablesByEventId(eventId).subscribe({
      next: (tables) => {
        this.tables = tables;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading tables:', error);
        this.errorMessage = 'Une erreur est survenue lors du chargement des tables.';
        this.isLoading = false;
      }
    });
  }

  selectTable(tableId: string): void {
    this.selectedTableId = tableId;
    this.selectedTable = this.tables.find(t => t.id === tableId);
    this.step = 'form';
  }

  isTableAvailable(table: Table): boolean {
    return table.isAvailable;
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', { day: 'numeric', month: 'long', year: 'numeric' });
  }

  onReservationSubmit(formData: ReservationFormData): void {
    if (!this.event || !this.selectedTableId) {
      return;
    }

    const totalPrice = this.event.pricePerPerson * formData.numberOfGuests;

    this.reservationService.createReservation(
      this.event.id,
      this.selectedTableId,
      formData,
      totalPrice
    ).subscribe({
      next: (reservation) => {
        // Navigate with documentId (reservation.id now contains documentId)
        this.router.navigate(['/payment', reservation.id]);
      },
      error: (error) => {
        console.error('Error creating reservation:', error);
        this.errorMessage = 'Une erreur est survenue lors de la création de la réservation.';
      }
    });
  }

  goBack(): void {
    if (this.step === 'form') {
      this.step = 'table-selection';
      this.selectedTableId = null;
      this.selectedTable = undefined;
    } else {
      this.router.navigate(['/events', this.event?.id]);
    }
  }
}
