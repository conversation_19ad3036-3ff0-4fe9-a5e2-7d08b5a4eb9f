<div class="container mx-auto px-4 py-12">
  <div class="max-w-4xl mx-auto">
    <!-- Loading Indicator -->
    <div *ngIf="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gold-600"></div>
    </div>

    <!-- Error Message -->
    <div *ngIf="errorMessage" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
      {{ errorMessage }}
    </div>

    <!-- Success Message -->
    <div *ngIf="successMessage" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
      {{ successMessage }}
    </div>

    <!-- Reservation Process -->
    <div *ngIf="!isLoading && event">
      <!-- Header -->
      <div class="mb-8">
        <button
          (click)="goBack()"
          class="flex items-center text-gold-600 hover:text-gold-700 mb-4"
        >
          <span class="material-icons mr-1">arrow_back</span>
          <span>Retour</span>
        </button>
        <h1 class="text-3xl font-bold text-gray-900">Réservation</h1>
        <p class="text-gray-600 mt-2">{{ event.title }} - {{ formatDate(event.date) }} à {{ event.time }}</p>
      </div>

      <!-- Steps Indicator -->
      <div class="flex items-center mb-8">
        <div class="flex items-center">
          <div class="flex items-center justify-center w-10 h-10 rounded-full bg-gold-600 text-white">
            1
          </div>
          <span class="ml-2 font-medium" [ngClass]="{'text-gold-600': step === 'table-selection', 'text-gray-600': step !== 'table-selection'}">
            Sélection de table
          </span>
        </div>
        <div class="flex-1 h-px bg-gray-300 mx-4"></div>
        <div class="flex items-center">
          <div class="flex items-center justify-center w-10 h-10 rounded-full" [ngClass]="{'bg-gold-600 text-white': step === 'form', 'bg-gray-300 text-gray-600': step !== 'form'}">
            2
          </div>
          <span class="ml-2 font-medium" [ngClass]="{'text-gold-600': step === 'form', 'text-gray-600': step !== 'form'}">
            Informations
          </span>
        </div>
        <div class="flex-1 h-px bg-gray-300 mx-4"></div>
        <div class="flex items-center">
          <div class="flex items-center justify-center w-10 h-10 rounded-full bg-gray-300 text-gray-600">
            3
          </div>
          <span class="ml-2 font-medium text-gray-600">Paiement</span>
        </div>
      </div>

      <!-- Table Selection Step -->
      <div *ngIf="step === 'table-selection'" class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-2xl font-semibold mb-6">Sélectionnez une table</h2>

        <div *ngIf="tables.length === 0" class="text-gray-600 py-4">
          Aucune table disponible pour cet événement.
        </div>

        <div *ngIf="tables.length > 0" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-6">
          <button
            *ngFor="let table of tables"
            (click)="selectTable(table.id)"
            class="p-4 rounded-md text-center transition-colors"
            [ngClass]="{
              'bg-green-100 text-green-800 hover:bg-green-200': isTableAvailable(table),
              'bg-red-100 text-red-800 cursor-not-allowed': !isTableAvailable(table)
            }"
            [disabled]="!isTableAvailable(table)"
          >
            <div class="font-medium">Table {{ table.tableNumber }}</div>
            <div class="text-sm">{{ table.capacity }} personnes</div>
            <div *ngIf="!isTableAvailable(table)" class="text-xs mt-1">Réservée</div>
          </button>
        </div>
      </div>

      <!-- Reservation Form Step -->
      <div *ngIf="step === 'form' && selectedTable && event" class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-2xl font-semibold mb-6">Informations de réservation</h2>
        <div class="mb-6 p-4 bg-gray-50 rounded-md">
          <div class="flex justify-between mb-2">
            <span class="text-gray-600">Événement:</span>
            <span class="font-medium">{{ event.title }}</span>
          </div>
          <div class="flex justify-between mb-2">
            <span class="text-gray-600">Date:</span>
            <span class="font-medium">{{ formatDate(event.date) }} à {{ event.time }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">Table sélectionnée:</span>
            <span class="font-medium">Table {{ selectedTable.tableNumber }} ({{ selectedTable.capacity }} personnes)</span>
          </div>
        </div>

        <app-reservation-form
          [event]="event"
          [selectedTableId]="selectedTableId!"
          [selectedTable]="selectedTable"
          (formSubmit)="onReservationSubmit($event)"
        ></app-reservation-form>
      </div>
    </div>
  </div>
</div>
