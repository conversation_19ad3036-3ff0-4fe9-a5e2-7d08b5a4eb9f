# Email Notification Setup Guide

This guide will help you set up and test the email notification functionality for your hotel booking application.

## 📋 Overview

The email system automatically sends:
1. **Reservation Confirmation Email** - When a new reservation is created
2. **Payment Success Email** - When a reservation status is updated to 'confirmed'

## 🔧 Backend Setup

### 1. File Structure

Make sure you have created these files in your Strapi backend:

```
strapi-backend/
├── config/
│   └── plugins.js                    # Email plugin configuration
├── src/
│   └── api/
│       ├── email/
│       │   ├── controllers/
│       │   │   └── email.js          # Email controller
│       │   ├── routes/
│       │   │   └── email.js          # Email routes
│       │   └── services/
│       │       ├── email-templates.js # Email templates
│       │       └── email-notification.js # Email notification service
│       └── reservation/
│           └── content-types/
│               └── reservation/
│                   └── lifecycles.js # Reservation lifecycle hooks
└── .env                              # Environment variables
```

### 2. Environment Variables

Copy the `.env.example` file to `.env` and configure your email settings:

```bash
# Email Configuration
EMAIL_DEFAULT_FROM=<EMAIL>
EMAIL_DEFAULT_REPLY_TO=<EMAIL>

# SendGrid Configuration (Recommended for production)
EMAIL_PROVIDER=sendgrid
SENDGRID_API_KEY=your_sendgrid_api_key_here

# Alternative: Nodemailer SMTP Configuration
# EMAIL_PROVIDER=nodemailer
# EMAIL_SMTP_HOST=smtp.gmail.com
# EMAIL_SMTP_PORT=587
# EMAIL_SMTP_USERNAME=<EMAIL>
# EMAIL_SMTP_PASSWORD=your_app_password

# Hotel Information (for email templates)
HOTEL_NAME=Your Hotel Name
HOTEL_ADDRESS=123 Hotel Street, City, Country
HOTEL_PHONE=******-567-8900
HOTEL_EMAIL=<EMAIL>
HOTEL_WEBSITE=https://yourhotel.com

# Test Email Address
EMAIL_TEST_ADDRESS=<EMAIL>
```

### 3. Install Email Plugin

Make sure the Strapi email plugin is installed:

```bash
cd your-strapi-backend
npm install @strapi/plugin-email
```

### 4. Restart Strapi

After setting up the configuration, restart your Strapi server:

```bash
npm run develop
```

## 🧪 Testing the Email Functionality

### Test 1: Email Configuration Test

First, verify that your email configuration is working:

```bash
# Test email configuration
curl -X GET "http://localhost:1337/api/email/test-config" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Expected response:
```json
{
  "message": "Email configuration test successful",
  "config": {
    "defaultFrom": "<EMAIL>",
    "testAddress": "<EMAIL>"
  }
}
```

### Test 2: Send Test Email

Send a test email to verify templates are working:

```bash
# Send test email
curl -X POST "http://localhost:1337/api/email/test" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"email": "<EMAIL>"}'
```

### Test 3: Complete Reservation Flow

1. **Create a Reservation** (via Angular frontend):
   - Go to an event page
   - Select a table
   - Fill out the reservation form
   - Submit the reservation
   - ✅ Check that a reservation confirmation email is sent

2. **Complete Payment** (via Angular frontend):
   - Go to the payment page
   - Complete the payment process
   - ✅ Check that a payment success email is sent

### Test 4: Manual Email Triggers

You can manually trigger emails using the API:

```bash
# Send reservation confirmation email
curl -X POST "http://localhost:1337/api/email/reservation-confirmation/RESERVATION_DOCUMENT_ID" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Send payment confirmation email
curl -X POST "http://localhost:1337/api/email/payment-confirmation/RESERVATION_DOCUMENT_ID" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🔍 Troubleshooting

### Common Issues

1. **Emails not being sent**
   - Check Strapi console for error messages
   - Verify environment variables are set correctly
   - Test email configuration endpoint

2. **Missing user or event data**
   - Ensure reservations are created with proper user and event relations
   - Check that user has an email field
   - Verify event data is populated correctly

3. **Email templates not loading**
   - Check that email-templates.js service is in the correct location
   - Verify service is properly exported
   - Check Strapi logs for service loading errors

4. **Lifecycle hooks not triggering**
   - Ensure lifecycles.js is in the correct path: `src/api/reservation/content-types/reservation/lifecycles.js`
   - Check Strapi logs for lifecycle execution
   - Verify reservation creation/update is working

### Debug Mode

Enable debug logging by adding to your Strapi configuration:

```javascript
// config/logger.js
module.exports = {
  transports: [
    {
      type: 'console',
      options: {
        level: 'debug',
      },
    },
  ],
};
```

### Email Provider Specific Issues

**SendGrid:**
- Verify API key is valid
- Check SendGrid dashboard for delivery status
- Ensure sender email is verified in SendGrid

**SMTP/Gmail:**
- Use App Passwords instead of regular passwords
- Enable 2-factor authentication
- Check Gmail security settings

## 📧 Email Templates

The system includes two email templates:

1. **Reservation Confirmation** - Sent when reservation is created
   - Includes reservation details
   - Shows pending payment status
   - Provides next steps

2. **Payment Success** - Sent when payment is confirmed
   - Confirms payment completion
   - Shows final reservation details
   - Includes arrival instructions

Both templates are responsive and include:
- Hotel branding
- Professional styling
- Clear call-to-action
- Contact information

## 🚀 Production Deployment

For production deployment:

1. **Use SendGrid** for reliable email delivery
2. **Set up proper DNS records** (SPF, DKIM, DMARC)
3. **Configure email monitoring** and delivery tracking
4. **Test thoroughly** with real email addresses
5. **Set up email bounce handling**

## 📞 Support

If you encounter issues:

1. Check Strapi console logs
2. Verify all environment variables
3. Test email configuration endpoint
4. Check email provider dashboard
5. Review this guide for common solutions

The email system is designed to be robust and will not break the reservation process if emails fail to send - errors are logged but don't prevent reservations from being created or updated.
