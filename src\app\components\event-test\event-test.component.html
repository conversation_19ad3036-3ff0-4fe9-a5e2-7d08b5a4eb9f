<div class="container mx-auto px-4 py-12">
  <h1 class="text-3xl font-bold text-center mb-8">Test d'Événements (Données JSON)</h1>
  
  <!-- Error Message -->
  <div *ngIf="errorMessage" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
    {{ errorMessage }}
    <button (click)="parseJsonData()" class="ml-4 bg-red-700 text-white px-4 py-1 rounded hover:bg-red-800">
      Réessayer
    </button>
  </div>
  
  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gold-600"></div>
  </div>
  
  <!-- No Results Message -->
  <div *ngIf="!isLoading && !errorMessage && events.length === 0" class="text-center py-12">
    <p class="text-gray-600 text-lg">Aucun événement trouvé dans les données JSON.</p>
  </div>
  
  <!-- Events Grid -->
  <div *ngIf="!isLoading && !errorMessage && events.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
    <app-event-card *ngFor="let event of events" [event]="event"></app-event-card>
  </div>
</div>
