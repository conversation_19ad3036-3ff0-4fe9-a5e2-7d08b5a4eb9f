import { Injectable } from '@angular/core';
import { Observable, of, throwError } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { Event, Restaurant } from '../models/event.model';
import { ApiService } from './api.service';
import { environment } from '../../environments/environment';

// Original Strapi v4 response format
interface StrapiResponse<T> {
  data: Array<{
    id: number;
    attributes: T & {
      createdAt: string;
      updatedAt: string;
      publishedAt: string;
    };
  }>;
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}





interface StrapiEventAttributes {
  title?: string;
  description?: string;
  date?: string;
  time?: string;
  maxCapacity?: number;
  pricePerPerson?: number;
  is_active?: boolean;
  createdAt?: string;
  updatedAt?: string;
  publishedAt?: string;
  restaurant?: {
    data?: {
      id: number;
      attributes?: {
        name?: string;
        description?: string;
        createdAt?: string;
        updatedAt?: string;
      };
    };
  };
  icon?: {
    data?: {
      id: number;
      attributes?: {
        url?: string;
      };
    } | null;
  };
  images?: {
    data?: Array<{
      id: number;
      attributes?: {
        url?: string;
      };
    }>;
  };
}

@Injectable({
  providedIn: 'root'
})
export class EventService {
  // Fallback images if no images are provided
  private defaultImageUrl = 'assets/images/default-event.jpg';
  private fallbackImages = [
    'assets/images/event1.jpg',
    'assets/images/event2.jpg',
    'assets/images/event3.jpg',
    'assets/images/event4.jpg',
    'assets/images/event5.jpg',
    'assets/images/event6.jpg'
  ];

  constructor(private apiService: ApiService) { }

  /**
   * Parse events from JSON data
   * This method can be used to directly parse JSON data received from the API
   */
  parseEventsFromJson(jsonData: any): Event[] {
    if (!jsonData || !jsonData.data || !Array.isArray(jsonData.data)) {
      console.error('Invalid JSON data format');
      return [];
    }

    return jsonData.data.map((eventData: any) => this.mapNewStrapiEvent(eventData));
  }

  /**
   * Map new Strapi event format to our Event model
   */
  private mapNewStrapiEvent(eventData: any): Event {
    // Extract time from the date string (which is in ISO format)
    const dateObj = new Date(eventData.date);
    const formattedDate = dateObj.toISOString().split('T')[0]; // YYYY-MM-DD
    const hours = dateObj.getHours().toString().padStart(2, '0');
    const minutes = dateObj.getMinutes().toString().padStart(2, '0');
    const formattedTime = `${hours}:${minutes}`;

    // Handle images
    let imageUrls: string[] = [];
    let mainImageUrl = this.defaultImageUrl;

    if (eventData.images && eventData.images.length > 0) {
      // Map image URLs, adding the API base URL if needed
      imageUrls = eventData.images.map((img: any) => {
        if (!img.url) return '';

        // Check if the URL is relative or absolute
        if (img.url.startsWith('/')) {
          return `${environment.apiUrl}${img.url}`;
        } else if (!img.url.startsWith('http://') && !img.url.startsWith('https://') && !img.url.startsWith('assets/')) {
          return `${environment.apiUrl}/${img.url}`;
        }
        return img.url;
      }).filter((url: string) => !!url); // Remove any empty URLs

      // Use the first image as the main image
      if (imageUrls.length > 0) {
        mainImageUrl = imageUrls[0];
      }
    } else {
      // Use a random fallback image
      const randomIndex = Math.floor(Math.random() * this.fallbackImages.length);
      mainImageUrl = this.fallbackImages[randomIndex];
    }

    // Handle icon if present
    let iconUrl: string | null = null;
    if (eventData.icon && eventData.icon.url) {
      if (eventData.icon.url.startsWith('/')) {
        iconUrl = `${environment.apiUrl}${eventData.icon.url}`;
      } else if (!eventData.icon.url.startsWith('http://') && !eventData.icon.url.startsWith('https://') && !eventData.icon.url.startsWith('assets/')) {
        iconUrl = `${environment.apiUrl}/${eventData.icon.url}`;
      } else {
        iconUrl = eventData.icon.url;
      }
    }

    return {
      id: eventData.id.toString(),
      title: eventData.name || '',
      description: eventData.description || '',
      date: formattedDate,
      time: formattedTime,
      imageUrl: mainImageUrl,
      images: imageUrls,
      iconUrl: iconUrl,
      restaurantId: '0', // Default value as restaurant info is not in the new format
      maxCapacity: eventData.max_attendees || 0,
      pricePerPerson: parseFloat(eventData.price) || 0,
      isActive: eventData.is_active || false,
      createdAt: eventData.createdAt || '',
      updatedAt: eventData.updatedAt || ''
    };
  }

  /**
   * Map Strapi event to our Event model
   */
  private mapStrapiEvent(eventData: { id: number; attributes: StrapiEventAttributes }): Event {
    // Safely handle potentially undefined properties
    const imagesData = eventData.attributes.images?.data || [];
    const iconData = eventData.attributes.icon?.data || null;

    // Create image URLs array safely and ensure they're absolute URLs
    const imageUrls = imagesData.length > 0
      ? imagesData.map(img => {
          const url = img.attributes?.url || '';
          if (!url) return '';

          // Check if the URL is relative or absolute
          if (url.startsWith('/')) {
            return `${environment.apiUrl}${url}`;
          } else if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('assets/')) {
            return `${environment.apiUrl}/${url}`;
          }
          return url;
        }).filter((url: string) => !!url) // Remove any empty URLs
      : [];

    // If no images, use the icon or a default image
    let mainImageUrl = this.defaultImageUrl;
    if (imageUrls.length > 0 && imageUrls[0]) {
      mainImageUrl = imageUrls[0];
    } else if (iconData && iconData.attributes && iconData.attributes.url) {
      const iconUrl = iconData.attributes.url;
      if (iconUrl.startsWith('/')) {
        mainImageUrl = `${environment.apiUrl}${iconUrl}`;
      } else if (!iconUrl.startsWith('http://') && !iconUrl.startsWith('https://') && !iconUrl.startsWith('assets/')) {
        mainImageUrl = `${environment.apiUrl}/${iconUrl}`;
      } else {
        mainImageUrl = iconUrl;
      }
    } else {
      // Use a random fallback image
      const randomIndex = Math.floor(Math.random() * this.fallbackImages.length);
      mainImageUrl = this.fallbackImages[randomIndex];
    }

    // Get restaurant ID safely
    const restaurantData = eventData.attributes.restaurant?.data;
    const restaurantId = restaurantData ? restaurantData.id.toString() : '0';

    return {
      id: eventData.id.toString(),
      title: eventData.attributes.title || '',
      description: eventData.attributes.description || '',
      date: eventData.attributes.date || '',
      time: eventData.attributes.time || '',
      imageUrl: mainImageUrl,
      images: imageUrls,
      iconUrl: iconData && iconData.attributes && iconData.attributes.url ?
        (iconData.attributes.url.startsWith('/') ?
          `${environment.apiUrl}${iconData.attributes.url}` :
          (!iconData.attributes.url.startsWith('http://') && !iconData.attributes.url.startsWith('https://') && !iconData.attributes.url.startsWith('assets/') ?
            `${environment.apiUrl}/${iconData.attributes.url}` :
            iconData.attributes.url)) :
        null,
      restaurantId: restaurantId,
      maxCapacity: eventData.attributes.maxCapacity || 0,
      pricePerPerson: eventData.attributes.pricePerPerson || 0,
      isActive: eventData.attributes.is_active || false,
      createdAt: eventData.attributes.createdAt || '',
      updatedAt: eventData.attributes.updatedAt || ''
    };
  }

  /**
   * Get all events
   */
  getAllEvents(): Observable<Event[]> {
    return this.apiService.get<any>(
      'events?populate=*'
    ).pipe(
      map(response => {
        // Check which format the response is in
        if (response && response.data) {
          // Check if it's the new format (direct data objects)
          if (response.data.length > 0 && 'name' in response.data[0]) {
            console.log('Using new Strapi format');
            return response.data.map((eventData: any) => this.mapNewStrapiEvent(eventData));
          }
          // It's the old format (with attributes)
          else if (response.data.length > 0 && 'attributes' in response.data[0]) {
            console.log('Using old Strapi format');
            return response.data.map((eventData: any) => this.mapStrapiEvent(eventData));
          }
        }

        // If we can't determine the format or there's no data, return empty array
        console.warn('Unknown data format or no data, returning empty array');
        return [];
      }),
      catchError(error => {
        console.error('Error fetching events', error);
        // Return mock data for development purposes
        return of(this.getMockEvents());
      })
    );
  }

  /**
   * Get mock events for development
   */
  private getMockEvents(): Event[] {
    return [
      {
        id: '1',
        title: 'Soirée Gastronomique Tunisienne',
        description: 'Découvrez les saveurs authentiques de la cuisine tunisienne lors de cette soirée exceptionnelle.',
        date: '2025-05-15',
        time: '19:30',
        imageUrl: 'assets/images/event1.jpg',
        restaurantId: '1',
        maxCapacity: 50,
        pricePerPerson: 120,
        createdAt: '2025-04-01',
        updatedAt: '2025-04-01'
      },
      {
        id: '2',
        title: 'Dîner Méditerranéen',
        description: 'Un voyage culinaire à travers la Méditerranée avec des plats frais et savoureux.',
        date: '2025-05-22',
        time: '20:00',
        imageUrl: 'assets/images/event2.jpg',
        restaurantId: '2',
        maxCapacity: 40,
        pricePerPerson: 150,
        createdAt: '2025-04-02',
        updatedAt: '2025-04-02'
      },
      {
        id: '3',
        title: 'Brunch de Luxe',
        description: 'Commencez votre journée avec un brunch de luxe comprenant une sélection de mets raffinés.',
        date: '2025-05-25',
        time: '11:00',
        imageUrl: 'assets/images/event3.jpg',
        restaurantId: '3',
        maxCapacity: 30,
        pricePerPerson: 90,
        createdAt: '2025-04-03',
        updatedAt: '2025-04-03'
      },
      {
        id: '4',
        title: 'Soirée Dégustation de Vins',
        description: 'Une soirée dédiée à la découverte des meilleurs vins tunisiens et internationaux.',
        date: '2025-06-05',
        time: '20:00',
        imageUrl: 'assets/images/event4.jpg',
        restaurantId: '1',
        maxCapacity: 25,
        pricePerPerson: 180,
        createdAt: '2025-04-05',
        updatedAt: '2025-04-05'
      },
      {
        id: '5',
        title: 'Dîner Fruits de Mer',
        description: 'Un festin de fruits de mer frais pêchés dans les eaux cristallines de la Méditerranée.',
        date: '2025-06-12',
        time: '19:30',
        imageUrl: 'assets/images/event5.jpg',
        restaurantId: '2',
        maxCapacity: 35,
        pricePerPerson: 200,
        createdAt: '2025-04-10',
        updatedAt: '2025-04-10'
      },
      {
        id: '6',
        title: 'Cuisine Fusion Asiatique',
        description: 'Une expérience culinaire unique mêlant les saveurs asiatiques et méditerranéennes.',
        date: '2025-06-18',
        time: '20:00',
        imageUrl: 'assets/images/event6.jpg',
        restaurantId: '3',
        maxCapacity: 40,
        pricePerPerson: 170,
        createdAt: '2025-04-15',
        updatedAt: '2025-04-15'
      }
    ];
  }

  /**
   * Get upcoming events
   */
  getUpcomingEvents(limit: number = 3): Observable<Event[]> {
    const today = new Date().toISOString().split('T')[0]; // Format: YYYY-MM-DD

    return this.apiService.get<any>(
      'events?populate=*'
    ).pipe(
      map(response => {
        let events: Event[] = [];

        // Check which format the response is in
        if (response && response.data) {
          // Check if it's the new format (direct data objects)
          if (response.data.length > 0 && 'name' in response.data[0]) {
            console.log('Using new Strapi format for upcoming events');
            events = response.data.map((eventData: any) => this.mapNewStrapiEvent(eventData));
          }
          // It's the old format (with attributes)
          else if (response.data.length > 0 && 'attributes' in response.data[0]) {
            console.log('Using old Strapi format for upcoming events');
            events = response.data.map((eventData: any) => this.mapStrapiEvent(eventData));
          }
        }

        // Client-side filtering for upcoming events
        const upcomingEvents = events
          .filter(event => event.date >= today)
          .sort((a, b) => a.date.localeCompare(b.date))
          .slice(0, limit);

        return upcomingEvents;
      }),
      catchError(error => {
        console.error('Error fetching upcoming events', error);
        // Return mock data for development purposes
        return of(this.getMockEvents().slice(0, limit));
      })
    );
  }

  getDocumentIdById(id: string): Observable<string | undefined> {
    return this.apiService.get<any>(
      `events`
    ).pipe(
      map(response => {
        if (response && response.data && response.data.length > 0) {
          const event = response.data.find((eventData: any) => eventData.id.toString() === id);
          if (event) {
            return event.documentId;
          }
        } return undefined;
      }),
      catchError(error => {
        console.error(`Error fetching document ID for event with ID ${id}`, error);
        return of(undefined);
      })
    );
  }

  /**
   * Get event by ID (handles both numeric ID and documentId)
   */
  getEventById(id: string): Observable<Event | undefined> {
    console.log('Fetching event with ID:', id);

    // First, try to get the event directly using the provided ID (could be documentId)
    return this.apiService.get<any>(`events/${id}?populate=*`).pipe(
      map(response => {
        const event = this.processEventResponse(response);
        if (event) {
          console.log('Found event directly with ID:', id);
          return event;
        }
        return undefined;
      }),
      catchError(error => {
        console.log('Direct fetch failed, trying to find by documentId in list...');
        // If direct fetch fails, try to find the event in the list by documentId
        return this.apiService.get<any>('events?populate=*').pipe(
          map(response => {
            if (response && response.data && Array.isArray(response.data)) {
              // Find event by documentId or numeric id
              const eventData = response.data.find((event: any) =>
                event.documentId === id || event.id.toString() === id
              );

              if (eventData) {
                console.log('Found event in list:', eventData);
                // Check if it's the new format
                if ('name' in eventData) {
                  return this.mapNewStrapiEvent(eventData);
                }
                // It's the old format
                else if ('attributes' in eventData) {
                  return this.mapStrapiEvent(eventData);
                }
              }
            }
            return undefined;
          }),
          catchError(listError => this.handleEventError(listError, id))
        );
      })
    );
  }

  private processEventResponse(response: any): Event | undefined {
    // Check which format the response is in
    if (response && response.data) {
      // When using filters[documentId], we get an array of events
      if (Array.isArray(response.data) && response.data.length > 0) {
        const eventData = response.data[0];
        // Check if it's the new format
        if ('name' in eventData) {
          console.log('Using new Strapi format for single event (array)');
          return this.mapNewStrapiEvent(eventData);
        }
        // It's the old format
        else if ('attributes' in eventData) {
          console.log('Using old Strapi format for single event (array)');
          return this.mapStrapiEvent(eventData);
        }
      }
      // Single event response format (original)
      else if ('name' in response.data) {
        console.log('Using new Strapi format for single event');
        return this.mapNewStrapiEvent(response.data);
      }
      // It's the old format (with attributes)
      else if ('attributes' in response.data) {
        console.log('Using old Strapi format for single event');
        return this.mapStrapiEvent(response.data);
      }
    }

    // If we can't determine the format or there's no data, return undefined
    console.warn('Unknown data format or no data for event');
    return undefined;
  }

  private handleEventError(error: any, id: string): Observable<Event | undefined> {
    console.error(`Error fetching event with ID ${id}`, error);
    // Return mock data for development purposes
    const mockEvent = this.getMockEvents().find(event => event.id === id);
    if (mockEvent) {
      return of(mockEvent);
    }
    return throwError(() => new Error('Événement non trouvé'));
  }
  /**
   * Search events
   */
  searchEvents(query: string, date?: string): Observable<Event[]> {
    return this.apiService.get<any>('events?populate=*').pipe(
      map(response => {
        let events: Event[] = [];

        // Check which format the response is in
        if (response && response.data) {
          // Check if it's the new format (direct data objects)
          if (response.data.length > 0 && 'name' in response.data[0]) {
            console.log('Using new Strapi format for search');
            events = response.data.map((eventData: any) => this.mapNewStrapiEvent(eventData));
          }
          // It's the old format (with attributes)
          else if (response.data.length > 0 && 'attributes' in response.data[0]) {
            console.log('Using old Strapi format for search');
            events = response.data.map((eventData: any) => this.mapStrapiEvent(eventData));
          }
        }

        // Client-side filtering for search
        let filteredEvents = events;

        if (query) {
          const lowerQuery = query.toLowerCase();
          filteredEvents = filteredEvents.filter(event =>
            event.title.toLowerCase().includes(lowerQuery) ||
            event.description.toLowerCase().includes(lowerQuery)
          );
        }

        if (date) {
          filteredEvents = filteredEvents.filter(event => event.date === date);
        }

        return filteredEvents;
      }),
      catchError(error => {
        console.error('Error searching events', error);
        // Return filtered mock data for development purposes
        let filteredEvents = this.getMockEvents();

        if (query) {
          const lowerQuery = query.toLowerCase();
          filteredEvents = filteredEvents.filter(event =>
            event.title.toLowerCase().includes(lowerQuery) ||
            event.description.toLowerCase().includes(lowerQuery)
          );
        }

        if (date) {
          filteredEvents = filteredEvents.filter(event => event.date === date);
        }

        return of(filteredEvents);
      })
    );
  }

  /**
   * Get active events
   */
  getActiveEvents(): Observable<Event[]> {
    return this.apiService.get<any>('events?populate=*').pipe(
      map(response => {
        let events: Event[] = [];

        // Check which format the response is in
        if (response && response.data) {
          // Check if it's the new format (direct data objects)
          if (response.data.length > 0 && 'name' in response.data[0]) {
            console.log('Using new Strapi format for active events');
            events = response.data.map((eventData: any) => this.mapNewStrapiEvent(eventData));
          }
          // It's the old format (with attributes)
          else if (response.data.length > 0 && 'attributes' in response.data[0]) {
            console.log('Using old Strapi format for active events');
            events = response.data.map((eventData: any) => this.mapStrapiEvent(eventData));
          }
        }

        // Client-side filtering for active events
        const activeEvents = events.filter(event => event.isActive === true);
        return activeEvents;
      }),
      catchError(error => {
        console.error('Error fetching active events', error);
        // Return mock data for development purposes
        // Assume all mock events are active
        return of(this.getMockEvents());
      })
    );
  }

  /**
   * Get restaurant by ID
   */
  getRestaurantById(id: string): Observable<Restaurant | undefined> {
    return this.apiService.get<any>('restaurants?populate=*').pipe(
      map(response => {
        if (response && response.data && Array.isArray(response.data)) {
          // Client-side filtering to find restaurant by ID
          const restaurantData = response.data.find((restaurant: any) =>
            restaurant.id.toString() === id || restaurant.documentId === id
          );

          if (restaurantData) {
            // Handle both old and new Strapi formats
            if ('attributes' in restaurantData) {
              // Old format with attributes
              return {
                id: restaurantData.id.toString(),
                name: restaurantData.attributes.name,
                description: restaurantData.attributes.description,
                imageUrl: '', // Add image handling if needed
                createdAt: restaurantData.attributes.createdAt,
                updatedAt: restaurantData.attributes.updatedAt
              };
            } else {
              // New format without attributes
              return {
                id: restaurantData.id.toString(),
                name: restaurantData.name,
                description: restaurantData.description,
                imageUrl: '', // Add image handling if needed
                createdAt: restaurantData.createdAt,
                updatedAt: restaurantData.updatedAt
              };
            }
          }
        }

        return undefined;
      }),
      catchError(error => {
        console.error(`Error fetching restaurant with ID ${id}`, error);
        // Return mock data for development purposes
        const mockRestaurants = [
          {
            id: '1',
            name: 'L\'Olivier',
            description: 'Restaurant méditerranéen avec vue sur la mer',
            imageUrl: 'assets/images/restaurant1.jpg',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: '2',
            name: 'Le Carthage',
            description: 'Cuisine tunisienne traditionnelle',
            imageUrl: 'assets/images/restaurant2.jpg',
            createdAt: '2025-01-02',
            updatedAt: '2025-01-02'
          },
          {
            id: '3',
            name: 'La Falaise',
            description: 'Spécialités de fruits de mer',
            imageUrl: 'assets/images/restaurant3.jpg',
            createdAt: '2025-01-03',
            updatedAt: '2025-01-03'
          }
        ];

        const mockRestaurant = mockRestaurants.find(r => r.id === id);
        if (mockRestaurant) {
          return of(mockRestaurant);
        }
        return throwError(() => new Error('Restaurant non trouvé'));
      })
    );
  }
}
