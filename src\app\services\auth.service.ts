import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { User } from '../models/user.model';
import { ApiService } from './api.service';

interface AuthResponse {
  jwt: string;
  user: {
    id: number;
    documentId?: string;
    username: string;
    email: string;
    provider: string;
    confirmed: boolean;
    blocked: boolean;
    createdAt: string;
    updatedAt: string;
    firstName?: string;
    lastName?: string;
    phoneNumber?: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();
  private tokenKey = 'auth_token';
  private userKey = 'user_data';

  constructor(private apiService: ApiService) {
    this.loadUserFromStorage();
  }

  /**
   * Load user from local storage on service initialization
   */
  private loadUserFromStorage(): void {
    const userData = localStorage.getItem(this.userKey);
    if (userData) {
      try {
        const user = JSON.parse(userData) as User;
        this.currentUserSubject.next(user);
      } catch (error) {
        console.error('Error parsing user data from localStorage', error);
        localStorage.removeItem(this.userKey);
        localStorage.removeItem(this.tokenKey);
      }
    }
  }

  /**
   * Save authentication data to local storage
   */
  private saveAuthData(token: string, user: User): void {
    localStorage.setItem(this.tokenKey, token);
    localStorage.setItem(this.userKey, JSON.stringify(user));
  }

  /**
   * Clear authentication data from local storage
   */
  private clearAuthData(): void {
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.userKey);
  }

  /**
   * Map Strapi user to our User model
   */
  private mapStrapiUser(userData: AuthResponse['user']): User {
    // Handle both direct properties and properties that might be in a nested structure
    return {
      id: userData.id.toString(),
      documentId: userData.documentId,
      email: userData.email,
      firstName: userData.firstName || '',
      lastName: userData.lastName || '',
      phoneNumber: userData.phoneNumber || '',
      createdAt: userData.createdAt,
      updatedAt: userData.updatedAt
    };
  }

  /**
   * Login user with email and password
   */
  login(email: string, password: string): Observable<User> {
    return this.apiService.post<AuthResponse>('auth/local', {
      identifier: email,
      password: password
    }).pipe(
      switchMap(response => {
        // Save the JWT token temporarily
        localStorage.setItem(this.tokenKey, response.jwt);

        // Fetch user details including documentId
        return this.apiService.get<any>('users/me').pipe(
          map(userDetails => {
            const user = this.mapStrapiUser({
              ...response.user,
              documentId: userDetails.documentId
            });
            this.saveAuthData(response.jwt, user);
            this.currentUserSubject.next(user);
            return user;
          })
        );
      }),
      catchError(error => {
        console.error('Login error', error);
        // Clean up token if login fails
        localStorage.removeItem(this.tokenKey);
        return throwError(() => new Error(error.message || 'Identifiants incorrects'));
      })
    );
  }

  /**
   * Register a new user
   */
  register(userData: Partial<User>, password: string): Observable<User> {
    // Format exactly as shown in Strapi documentation
    const requestData = {
      username: userData.email, // Using email as username
      email: userData.email,
      password: password
    };

    console.log('Registration request data:', requestData);

    return this.apiService.post<AuthResponse>('auth/local/register', requestData).pipe(
      switchMap(response => {
        console.log('Registration response:', response);
        // Save the JWT token temporarily
        localStorage.setItem(this.tokenKey, response.jwt);

        // Fetch user details including documentId
        return this.apiService.get<any>('users/me').pipe(
          map(userDetails => {
            const user = this.mapStrapiUser({
              ...response.user,
              documentId: userDetails.documentId
            });
            this.saveAuthData(response.jwt, user);
            this.currentUserSubject.next(user);
            return user;
          })
        );
      }),
      catchError(error => {
        console.error('Registration error', error);
        // Extract more detailed error message from Strapi response if available
        let errorMessage = 'Erreur lors de l\'inscription';
        if (error.error && error.error.error) {
          if (error.error.error.message) {
            errorMessage = error.error.error.message;
          } else if (error.error.error.details && error.error.error.details.errors) {
            errorMessage = error.error.error.details.errors
              .map((err: any) => err.message)
              .join(', ');
          }
        }
        return throwError(() => new Error(errorMessage));
      })
    );
  }

  /**
   * Logout user
   */
  logout(): void {
    this.clearAuthData();
    this.currentUserSubject.next(null);
  }

  /**
   * Get current user
   */
  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!this.currentUserSubject.value && !!localStorage.getItem(this.tokenKey);
  }

  /**
   * Get authentication token
   */
  getToken(): string | null {
    return localStorage.getItem(this.tokenKey);
  }

  /**
   * Refresh user data including documentId
   */
  refreshUserData(): Observable<User> {
    const currentUser = this.currentUserSubject.value;
    if (!currentUser) {
      return throwError(() => new Error('User not authenticated'));
    }

    return this.apiService.get<any>('users/me').pipe(
      map(userDetails => {
        const updatedUser: User = {
          ...currentUser,
          documentId: userDetails.documentId
        };
        this.saveAuthData(this.getToken()!, updatedUser);
        this.currentUserSubject.next(updatedUser);
        return updatedUser;
      }),
      catchError(error => {
        console.error('Error refreshing user data:', error);
        return throwError(() => new Error('Failed to refresh user data'));
      })
    );
  }

  /**
   * Update user profile
   */
  updateUserProfile(userData: Partial<User>): Observable<User> {
    const currentUser = this.currentUserSubject.value;
    if (!currentUser) {
      return throwError(() => new Error('User not authenticated'));
    }

    return this.apiService.put<{ id: string; [key: string]: any }>(`users/${currentUser.id}`, userData).pipe(
      map(response => {
        const updatedUser: User = {
          ...currentUser,
          ...userData,
          updatedAt: new Date().toISOString()
        };

        localStorage.setItem(this.userKey, JSON.stringify(updatedUser));
        this.currentUserSubject.next(updatedUser);
        return updatedUser;
      }),
      catchError(error => {
        console.error('Update profile error', error);
        return throwError(() => new Error(error.message || 'Erreur lors de la mise à jour du profil'));
      })
    );
  }

  /**
   * Send password reset email
   */
  forgotPassword(email: string): Observable<any> {
    return this.apiService.post('auth/forgot-password', {
      email: email
    }).pipe(
      catchError(error => {
        console.error('Forgot password error', error);
        return throwError(() => new Error(error.message || 'Erreur lors de l\'envoi de l\'email de réinitialisation'));
      })
    );
  }

  /**
   * Reset password with token
   */
  resetPassword(code: string, password: string, passwordConfirmation: string): Observable<any> {
    return this.apiService.post('auth/reset-password', {
      code,
      password,
      passwordConfirmation
    }).pipe(
      catchError(error => {
        console.error('Reset password error', error);
        return throwError(() => new Error(error.message || 'Erreur lors de la réinitialisation du mot de passe'));
      })
    );
  }

  /**
   * Send email confirmation
   */
  sendEmailConfirmation(email: string): Observable<any> {
    return this.apiService.post('auth/send-email-confirmation', {
      email
    }).pipe(
      catchError(error => {
        console.error('Send email confirmation error', error);
        return throwError(() => new Error(error.message || 'Erreur lors de l\'envoi de l\'email de confirmation'));
      })
    );
  }
}
